// TraceSlickGrid Hook Module
// This module contains the large TraceSlickGrid hook extracted from app.js

import { SlickGrid, SlickDataView, SlickGroupItemMetadataProvider } from "slickgrid"

// Hook for SlickGrid-based trace table with client-side filtering and pagination
const TraceSlickGrid = {
  mounted() {
    console.debug('TraceSlickGrid hook mounted');
    this.initializeSlickGrid();
    this.setupClientSideFiltering();
    this.setupStreamObserver();

    // Listen for grid data update events
    this.handleEvent("grid_data_update", (data) => {
      console.debug('Received grid_data_update event:', data);
      this.updateGridDataFromEvent(data);
    });

    // Listen for export events
    this.handleEvent("export_data", (data) => {
      console.debug('Received export_data event:', data);
      this.handleExportEvent(data);
    });

    // Export functionality is now handled server-side

    console.debug('TraceSlickGrid hook initialized');
  },

  updated() {
    console.debug('TraceSlickGrid hook updated');
    // Update filters when component data attributes change
    this.updateFiltersFromDataAttributes();

    // Since we use phx-update="ignore", we need to manually check for data changes
    // The data attributes won't be automatically updated by LiveView
    console.debug('TraceSlickGrid updated hook called');
  },

  destroyed() {
    console.debug('TraceSlickGrid hook destroyed - cleaning up grid instance');

    // Clean up stream observer
    if (this.streamObserver) {
      this.streamObserver.disconnect();
      this.streamObserver = null;
    }

    // Clean up resize listener
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }

    // Clean up SlickGrid instance
    if (this.grid) {
      try {
        // Unsubscribe from events to prevent memory leaks
        if (this.grid.onClick) {
          this.grid.onClick.unsubscribe();
        }

        // Destroy the grid instance
        this.grid.destroy();
        console.debug('SlickGrid instance destroyed');
      } catch (error) {
        console.warn('Error destroying SlickGrid instance:', error);
      }
      this.grid = null;
    }

    // Clean up DataView
    if (this.dataView) {
      try {
        // Unsubscribe from DataView events
        if (this.dataView.onRowCountChanged) {
          this.dataView.onRowCountChanged.unsubscribe();
        }
        if (this.dataView.onRowsChanged) {
          this.dataView.onRowsChanged.unsubscribe();
        }
        if (this.dataView.onGroupCollapsed) {
          this.dataView.onGroupCollapsed.unsubscribe();
        }
        if (this.dataView.onGroupExpanded) {
          this.dataView.onGroupExpanded.unsubscribe();
        }
      } catch (error) {
        console.warn('Error cleaning up DataView events:', error);
      }
      this.dataView = null;
    }

    // Clean up other references
    this.groupItemMetadataProvider = null;
    this.columns = null;
    this.options = null;
    this.filters = null;
    this.groupExpandStates = null;

    console.debug('TraceSlickGrid cleanup completed');
  },

  initializeSlickGrid() {
    const container = this.el;
    const brokerId = container.dataset.brokerName || 'default';

    console.debug('Initializing SlickGrid on container:', container);
    console.debug('Container ID:', container.id);
    console.debug('Broker ID:', brokerId);
    console.debug('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight);

    // Check if grid already exists to prevent double initialization
    if (this.grid) {
      console.warn('SlickGrid already initialized for broker:', brokerId, '- skipping initialization');
      return;
    }

    // Define columns for the trace grid with responsive width constraints
    this.columns = this.getResponsiveColumns();

    // Grid options - configured for read-only display like DaisyUI table
    this.options = {
      enableCellNavigation: true,
      enableColumnReorder: false,
      multiColumnSort: false,
      rowHeight: 32,
      headerRowHeight: 32,
      enableAsyncPostRender: false,
      forceFitColumns: this.shouldForceFitColumns(), // Dynamic based on screen size
      enableTextSelectionOnCells: false,
      viewportClass: 'trace-grid-viewport',
      editable: false,
      autoEdit: false,
      enableAddRow: false,
      enableCellRangeSelection: false,
      enableRowReordering: false,
      // Enable responsive behavior
      autoWidth: false, // Let columns use their defined widths
      syncColumnCellResize: true
    };

    // Initialize group item metadata provider for grouping functionality
    console.debug('Initializing SlickGroupItemMetadataProvider...');
    this.groupItemMetadataProvider = new SlickGroupItemMetadataProvider();
    console.debug('SlickGroupItemMetadataProvider initialized:', this.groupItemMetadataProvider);

    // Initialize data view for virtual scrolling and filtering
    this.dataView = new SlickDataView({
      groupItemMetadataProvider: this.groupItemMetadataProvider,
      inlineFilters: true
    });

    // Initialize filter state
    this.filters = {
      topic_filter: '',
      payload_filter: '',
      selected_client_ids: [],
      ignore_ping_packets: true,
      topic_grouping_enabled: false
    };

    // Initialize grouping state from data attributes
    const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
    this.groupingEnabled = topicGroupingEnabled;

    // Initialize group expand/collapse state storage
    this.groupExpandStates = new Map();

    // Initialize pending restoration flag
    this.pendingGroupStateRestoration = false;

    console.debug('Initial grouping state from data attributes:', topicGroupingEnabled);

    // Initialize the grid
    this.grid = new SlickGrid(container, this.dataView, this.columns, this.options);

    // Register the group item metadata provider to add expand/collapse group handlers
    this.grid.registerPlugin(this.groupItemMetadataProvider);

    // TODO: Re-enable failed ACK message styling after fixing group compatibility
    // For now, we'll disable this to ensure group functionality works correctly
    // this.dataView.getItemMetadata = this.getRowMetadata.bind(this);

    console.debug('SlickGrid initialized:', this.grid);
    console.debug('Grid viewport:', this.grid.getViewport());

    // Set up event handlers
    this.grid.onClick.subscribe((e, args) => {
      // Handle row clicks for message selection
      const item = this.dataView.getItem(args.row);
      if (item && !item.__group) {
        this.pushEventTo(this.el, 'select_message', { id: item.id });
      }
    });

    // Set up data view events
    this.dataView.onGroupCollapsed.subscribe((e, args) => {
      console.log('Group collapsed:', args.groupingKey);
      this.captureGroupStatesOnChange(args.groupingKey, false);        
      this.grid.render();

    });
    this.dataView.onGroupExpanded.subscribe((e, args) => {
      console.log('Group expanded:', args.groupingKey);
      this.captureGroupStatesOnChange(args.groupingKey, true);
      this.grid.render();

    });
    this.dataView.onRowsChanged.subscribe((e, args) => {
      // SlickGrid automatically handles rendering for onRowsChanged
      // Only invalidate specific rows if provided
      if (args.rows && args.rows.length > 0) {
        this.grid.invalidateRows(args.rows);
      } else {
        this.grid.invalidate();
      }

      this.updateMessageCount();

      // Handle pending group state restoration after grouping operations
      this.handlePendingGroupStateRestoration();
    });

    // Load initial data
    this.updateGridData();

    // Set up window resize listener for responsive behavior
    this.handleResize = this.debounce(() => {
      this.handleWindowResize();
    }, 250);

    window.addEventListener('resize', this.handleResize);

    // Force resize after initialization
    setTimeout(() => {
      this.grid.resizeCanvas();
      this.grid.render();
      console.debug('Grid resized and re-rendered');
      console.debug('Grid container HTML:', this.el.innerHTML.slice(0, 500));
      console.debug('Grid canvas element:', this.el.querySelector('.slick-viewport'));

      // Check computed styles
      const headerColumns = this.el.querySelector('.slick-header-columns');
      if (headerColumns) {
        const computedStyle = window.getComputedStyle(headerColumns);
        console.debug('Header columns computed style:', {
          left: computedStyle.left,
          width: computedStyle.width,
          position: computedStyle.position,
          display: computedStyle.display
        });
      }

      // Force autosizeColumns if available
      if (this.grid.autosizeColumns) {
        this.grid.autosizeColumns();
        console.debug('Autosized columns');
      }

      // Fix column positions
      this.fixColumnPositions();
    }, 100);
  },

  setupStreamObserver() {
    console.debug('Setting up stream observer');
    try {
      // Find the hidden stream container
      const streamContainer = document.getElementById('trace-messages-stream');
      if (!streamContainer) {
        console.warn('Stream container not found');
        return;
      }

      // Set up MutationObserver to watch for new stream items
      this.streamObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            // Check for added nodes (new messages)
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE && node.dataset.message) {
                try {
                  const messageData = JSON.parse(node.dataset.message);
                  console.debug('New message from stream:', messageData);

                  // Add the new message to the grid
                  this.addNewMessageToGrid(messageData);
                } catch (error) {
                  console.error('Error parsing stream message data:', error);
                }
              }
            });

            // Check for removed nodes (cleared messages)
            if (mutation.removedNodes.length > 0) {
              // If all children were removed, it means the stream was cleared
              if (streamContainer.children.length === 0) {
                console.debug('Stream cleared, clearing grid data');
                this.clearGridData();
              }
            }
          }
        });
      });

      // Start observing the stream container
      this.streamObserver.observe(streamContainer, {
        childList: true,
        subtree: false
      });

      console.debug('Stream observer setup completed');
    } catch (error) {
      console.error('Error setting up stream observer:', error);
    }
  },

  setupClientSideFiltering() {
    // Set up client-side filtering using SlickGrid's DataView
    try {
      console.debug('Setting up client-side filtering...');
      console.debug('DataView available:', !!this.dataView);
      console.debug('FilterFunction available:', !!this.filterFunction);

      // Initialize filters first before setting up filtering
      this.updateFiltersFromDataAttributes();

      // Now set up the filter function
      if (this.dataView) {
        // Create a filter function that has access to the hook's filters
        this.createAndSetFilterFunction();
        console.debug('Filter function set successfully');
      } else {
        console.warn('DataView not available for filtering setup');
      }
    } catch (error) {
      console.error('Error setting up client-side filtering:', error);
    }
  },

  // Create and set a filter function that works with SlickGrid's compilation
  createAndSetFilterFunction() {
    // Store a reference to the hook's filters in the DataView for access
    this.dataView._hookFilters = this.filters;
    this.dataView._hookInstance = this;

    // Create a filter function that can access the stored filters
    const filterFunction = function(item) {
      const filters = this._hookFilters;
      const hook = this._hookInstance;

      if (!filters) return true;

      // PING packet filter
      if (filters.ignore_ping_packets &&
          (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
        return false;
      }

      // Topic filter
      if (filters.topic_filter && filters.topic_filter.trim() !== '') {
        const topic = item.topic || '';
        if (!hook.matchesTopic(topic, filters.topic_filter)) {
          return false;
        }
      }

      // Payload filter
      if (filters.payload_filter && filters.payload_filter.trim() !== '') {
        const payload = item.payload || '';
        if (!payload.toLowerCase().includes(filters.payload_filter.toLowerCase())) {
          return false;
        }
      }

      // Client ID filter
      if (filters.selected_client_ids.length > 0) {
        if (!filters.selected_client_ids.includes(item.client_id)) {
          return false;
        }
      }

      return true;
    };

    this.dataView.setFilter(filterFunction);
  },

  // Update the filter function when filters change
  updateFilterFunction() {
    if (this.dataView) {
      this.dataView._hookFilters = this.filters;
      this.dataView.refresh();
    }
  },

  filterFunction(item) {
    // Apply all filters to determine if item should be visible

    // PING packet filter
    if (this.filters.ignore_ping_packets &&
        (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
      return false;
    }

    // Topic filter
    if (this.filters.topic_filter && this.filters.topic_filter.trim() !== '') {
      const topic = item.topic || '';
      if (!this.matchesTopic(topic, this.filters.topic_filter)) {
        return false;
      }
    }

    // Payload filter
    if (this.filters.payload_filter && this.filters.payload_filter.trim() !== '') {
      const payload = item.payload || '';
      if (!payload.toLowerCase().includes(this.filters.payload_filter.toLowerCase())) {
        return false;
      }
    }

    // Client ID filter
    if (this.filters.selected_client_ids.length > 0) {
      if (!this.filters.selected_client_ids.includes(item.client_id)) {
        return false;
      }
    }

    return true;
  },

  matchesTopic(topic, pattern) {
    // Simple MQTT topic matching - can be enhanced for wildcards
    if (pattern.includes('+') || pattern.includes('#')) {
      // Convert MQTT pattern to regex
      const regexPattern = pattern
        .replace(/\+/g, '[^/]+')
        .replace(/#/g, '.*');
      const regex = new RegExp(`^${regexPattern}$`);
      return regex.test(topic);
    } else {
      return topic.includes(pattern);
    }
  },

  updateFiltersFromDataAttributes() {
    // Read filter state from data attributes
    try {
      this.filters.topic_filter = this.el.dataset.topicFilter || '';
      this.filters.payload_filter = this.el.dataset.payloadFilter || '';
      this.filters.ignore_ping_packets = this.el.dataset.ignorePingPackets === 'true';
      this.filters.topic_grouping_enabled = this.el.dataset.topicGroupingEnabled === 'true';

      const selectedClientIds = this.el.dataset.selectedClientIds;
      if (selectedClientIds) {
        this.filters.selected_client_ids = JSON.parse(selectedClientIds);
      } else {
        this.filters.selected_client_ids = [];
      }

      // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
      const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
      this.groupingEnabled = topicGroupingEnabled;
      this.filters.topic_grouping_enabled = topicGroupingEnabled;

      console.debug('Synced grouping state from data attributes:', topicGroupingEnabled);

      // Update grouping state - this will also ensure filters are properly applied
      this.updateGrouping(topicGroupingEnabled);

    } catch (error) {
      console.error('Error reading filter data attributes:', error);
    }
  },

  updateFiltersFromEvent(data) {
    console.debug('updateFiltersFromEvent called with data:', data);

    // Update filters from LiveView event
    this.filters.topic_filter = data.topic_filter || '';
    this.filters.payload_filter = data.payload_filter || '';
    this.filters.selected_client_ids = data.selected_client_ids || [];
    this.filters.ignore_ping_packets = data.ignore_ping_packets;
    this.filters.topic_grouping_enabled = data.topic_grouping_enabled || false;

    // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
    const topicGroupingEnabled = data.topic_grouping_enabled || false;
    this.groupingEnabled = topicGroupingEnabled;
    this.filters.topic_grouping_enabled = topicGroupingEnabled;

    console.debug('Synced grouping state from event:', topicGroupingEnabled);

    // Update grouping state - this will also ensure filters are properly applied
    this.updateGrouping(topicGroupingEnabled);
  },

  updateGrouping(topicGroupingEnabled) {
    console.debug('updateGrouping called with:', topicGroupingEnabled);
    try {
      if (!this.dataView) {
        console.warn('DataView not available for grouping update');
        return;
      }

      if (topicGroupingEnabled) {
        // Enable topic grouping
        this.dataView.setGrouping([{
          getter: (item) => {
            // Handle both old string format and new serialized format
            if (typeof item.topic === 'string' && item.topic) {
              return item.topic;
            } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
              // Use the first topic from the topics array
              const firstTopic = item.topics[0];
              if (typeof firstTopic === 'object' && firstTopic.topic) {
                return firstTopic.topic;
              } else if (typeof firstTopic === 'string') {
                return firstTopic;
              }
            }
            return 'No Topic';
          },
          formatter: (g) => {
            // Format the group header with topic name and count
            const topicName = g.value || 'No Topic';
            return `<span class="font-semibold">Topic: ${topicName}</span> <span class="badge badge-sm badge-outline ml-2">${g.count} messages</span>`;
          },
          aggregateCollapsed: false, // Calculate aggregates for collapsed groups
          collapsed: true, // Start with groups collapsed by default
          lazyTotalsCalculation: true
        }]);

        console.debug('Topic grouping enabled');
      } else {
        // Disable grouping
        this.dataView.setGrouping([]);
        console.debug('Topic grouping disabled');
      }

      // Force grid to render with new grouping
      if (this.grid) {
        this.grid.invalidate();
        this.grid.render();
      }

      // Restore group states if grouping is enabled
      if (topicGroupingEnabled && this.groupStates) {
        this.handlePendingGroupStateRestoration();
      }

      // Update message count display to reflect the new grouping state
      this.updateMessageCount();

    } catch (error) {
      console.error('Error updating grouping:', error);
    }
  },

  updateGridData() {
    try {
      const gridDataJson = this.el.dataset.gridData;
      if (gridDataJson) {
        const gridData = JSON.parse(gridDataJson);

        // Validate that gridData is an array
        if (!Array.isArray(gridData)) {
          console.warn('Grid data is not an array:', gridData);
          return;
        }

        // Ensure all items have valid IDs, generate if missing
        const validGridData = gridData.map(item => {
          if (!item || typeof item !== 'object') {
            console.warn('Filtering out invalid item:', item);
            return null;
          }

          // Generate ID if missing or invalid
          if (!item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
            console.warn('Generating ID for item without valid id:', item);
            item.id = `generated_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          }

          // Ensure ID is a string for consistency
          item.id = String(item.id);

          return item;
        }).filter(item => item !== null);

        if (validGridData.length !== gridData.length) {
          console.warn(`Filtered out ${gridData.length - validGridData.length} invalid items`);
        }

        this.dataView.beginUpdate();
        // Use ID field for deduplication in all cases
        // SlickGrid requires unique IDs even when grouping is enabled
        this.dataView.setItems(validGridData, 'id');
        this.dataView.endUpdate();

        // Apply filters after loading data
        this.dataView.refresh();

        // Re-apply grouping if enabled, preserving existing group states
        if (this.groupingEnabled) {
          // Re-apply grouping configuration
          this.dataView.setGrouping([{
            getter: (item) => {
              // Extract topic for grouping - handle both direct topic field and topics array
              if (typeof item.topic === 'string' && item.topic) {
                return item.topic;
              } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
                const firstTopic = item.topics[0];
                if (typeof firstTopic === 'object' && firstTopic.topic) {
                  return firstTopic.topic;
                } else if (typeof firstTopic === 'string') {
                  return firstTopic;
                }
              }
              return 'No Topic';
            },
            formatter: (g) => {
              // Format the group header with topic name and count
              const topicName = g.value || 'No Topic';
              return `<span class="font-semibold">Topic: ${topicName}</span> <span class="badge badge-sm badge-outline ml-2">${g.count} messages</span>`;
            },
            aggregateCollapsed: false, // Calculate aggregates for collapsed groups
            collapsed: true, // Start with groups collapsed by default
            lazyTotalsCalculation: true
          }]);

          // Group state restoration will be handled by the event-driven mechanism
        }

        // Force grid to render
        this.grid.invalidate();
        this.grid.render();

        // Fix column positions after render
        this.fixColumnPositions();

        // Message count will be updated by onRowsChanged event

        // Log for debugging
        console.debug(`Updated grid with ${gridData.length} items`);
        console.debug('Grid data length:', this.dataView.getLength());
        console.debug('Grid viewport after update:', this.grid.getViewport());
      }
    } catch (error) {
      console.error('Error updating grid data:', error);
      console.error('Grid data JSON:', this.el.dataset.gridData);
    }
  },

  fixColumnPositions() {
    console.debug('fixColumnPositions called');

    // Let SlickGrid handle its own positioning
    // Force a resize to ensure proper column alignment
    if (this.grid && this.grid.resizeCanvas) {
      this.grid.resizeCanvas();
      console.debug('Grid canvas resized');
    }

    // Force column autosizing if available
    if (this.grid && this.grid.autosizeColumns) {
      this.grid.autosizeColumns();
      console.debug('Columns autosized');
    }
  },

  // Responsive helper methods
  shouldForceFitColumns() {
    // Always enable force fit to make table use full available width
    // This ensures the table adapts to both small and large screens
    return true;
  },

  getResponsiveColumns() {
    const screenWidth = window.innerWidth;
    const isMobile = screenWidth <= 480;
    const isTablet = screenWidth <= 768;

    // Base column definitions - use relative widths for forceFitColumns
    const baseColumns = [
      {
        id: "packet_id",
        name: "PACKET ID",
        field: "packet_id",
        // Use minWidth only to ensure readability, let forceFitColumns handle the rest
        minWidth: isMobile ? 60 : isTablet ? 70 : 80,
        formatter: this.packetIdFormatter
      },
      {
        id: "timestamp",
        name: "TIMESTAMP",
        field: "timestamp",
        minWidth: isMobile ? 100 : isTablet ? 120 : 140,
        formatter: this.timestampFormatter
      },
      {
        id: "client_id",
        name: "CLIENT ID",
        field: "client_id",
        minWidth: isMobile ? 80 : isTablet ? 90 : 100,
        formatter: this.clientIdFormatter
      },
      {
        id: "direction",
        name: "DIRECTION",
        field: "direction",
        minWidth: isMobile ? 60 : isTablet ? 70 : 80,
        formatter: this.directionFormatter
      },
      {
        id: "type",
        name: "TYPE",
        field: "type",
        minWidth: isMobile ? 70 : isTablet ? 80 : 90,
        formatter: this.typeFormatter
      },
      {
        id: "topic",
        name: "TOPIC",
        field: "topic",
        minWidth: isMobile ? 100 : isTablet ? 110 : 120,
        formatter: this.topicFormatter
      },
      {
        id: "payload",
        name: "PAYLOAD ｜ REASON",
        field: "payload",
        minWidth: isMobile ? 120 : isTablet ? 150 : 200,
        formatter: this.payloadFormatter
      },
      {
        id: "data_size",
        name: "PAYLOAD/TOTAL",
        field: "data_size",
        minWidth: isMobile ? 70 : isTablet ? 80 : 90,
        formatter: this.dataSizeFormatter
      },
      {
        id: "qos",
        name: "QOS",
        field: "qos",
        minWidth: isMobile ? 30 : isTablet ? 40 : 50
      },
      {
        id: "retain",
        name: "RETAIN",
        field: "retain",
        minWidth: isMobile ? 40 : isTablet ? 50 : 60,
        formatter: this.retainFormatter
      }
    ];

    // On very small screens, hide some less critical columns
    if (isMobile) {
      return baseColumns.filter(col =>
        !['data_size', 'qos', 'retain'].includes(col.id)
      );
    }

    return baseColumns;
  },

  // Formatters for different columns
  packetIdFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    return value === "undefined" ? 'N/A' : (value || 'N/A');
  },

  timestampFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    if (!value) return 'N/A';
    try {
      // Value is now integer microseconds since Unix epoch
      const date = new Date(value / 1000); // Convert microseconds to milliseconds

      // Get time zone from the grid element's data attribute
      const gridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
      const timeZone = gridElement ? gridElement.dataset.timeZone : 'UTC';

      // Convert to timezone to match backend formatting
      const options = {
        timeZone: timeZone,
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };

      const formatter = new Intl.DateTimeFormat('en-CA', options);
      const parts = formatter.formatToParts(date);

      const month = parts.find(part => part.type === 'month').value;
      const day = parts.find(part => part.type === 'day').value;
      const hour = parts.find(part => part.type === 'hour').value;
      const minute = parts.find(part => part.type === 'minute').value;
      const second = parts.find(part => part.type === 'second').value;

      // Get milliseconds from the original microsecond value
      const ms = String(Math.floor((value % 1000000) / 1000)).padStart(3, '0');

      return `${month}-${day} ${hour}:${minute}:${second}.${ms}`;
    } catch (error) {
      console.error('Error formatting timestamp:', error, 'value:', value);
      return 'Invalid';
    }
  },

  clientIdFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    return `<span">${value || 'unknown'}</span>`;
  },

  directionFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    let badgeClass, icon;
    if (value === 'IN') {
      badgeClass = 'badge-success';
      icon = '«';
    } else if (value === 'OUT') {
      badgeClass = 'badge-warning';
      icon = '»';
    } else if (value === 'SYSTEM') {
      badgeClass = 'badge-error';
      icon = '⚠';
    } else {
      badgeClass = 'badge-ghost';
      icon = '⚬';
    }
    return `<span class="badge badge-sm ${badgeClass}">${icon} ${value || 'N/A'}</span>`;
  },

  typeFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    // Inline the getTypeBadgeClass logic to avoid context issues
    let badgeClass;
    switch (value) {
      case 'PUBLISH':
      case 'PUBACK':
      case 'PUBREC':
      case 'PUBREL':
      case 'PUBCOMP':
        badgeClass = 'badge-info';
        break;
      case 'SUBSCRIBE':
      case 'SUBACK':
      case 'UNSUBSCRIBE':
      case 'UNSUBACK':
        badgeClass = 'badge-accent';
        break;
      case 'CONNECT':
      case 'CONNACK':
        badgeClass = 'badge-success';
        break;
      case 'DISCONNECT':
        badgeClass = 'badge-warning';
        break;
      case 'CONNECTION_ERROR':
        badgeClass = 'badge-error';
        break;
      case 'PINGREQ':
      case 'PINGRESP':
        badgeClass = 'badge-secondary';
        break;
      case 'AUTH':
        badgeClass = 'badge-primary';
        break;
      default:
        badgeClass = 'badge-ghost';
    }
    return `<span class="badge badge-sm badge-outline ${badgeClass}">${value}</span>`;
  },

  topicFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    // Handle both old string format and new serialized format
    let topicText = 'N/A';

    if (typeof value === 'string' && value) {
      topicText = value;
    } else if (dataContext && dataContext.topics && Array.isArray(dataContext.topics) && dataContext.topics.length > 0) {
      // Use the first topic from the topics array
      const firstTopic = dataContext.topics[0];
      if (typeof firstTopic === 'object' && firstTopic.topic) {
        topicText = firstTopic.topic;
      } else if (typeof firstTopic === 'string') {
        topicText = firstTopic;
      }
    }

    return `<span title="${topicText}">${topicText}</span>`;
  },

  payloadFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    if (!value) return '';

    // For ACK messages with reason code information, show full text without truncation
    const ackTypes = ['PUBACK', 'PUBREC', 'PUBREL', 'PUBCOMP', 'SUBACK', 'UNSUBACK', 'CONNACK', 'DISCONNECT'];
    if (dataContext && ackTypes.includes(dataContext.type) && value.includes('Success') || value.includes('Error') || value.includes('Refused') || value.includes('Subscriptions')) {
      return `<span class="font-mono text-sm" title="${value}">${value}</span>`;
    }
    return `<span class="font-mono" title="${value}">${value}</span>`;
  },

  retainFormatter(row, cell, value, columnDef, dataContext) {
    // For group rows, return empty string to let SlickGrid handle group row rendering
    if (dataContext && dataContext.__group) {
      return '';
    }
    return value ? 'Yes' : 'No';
  },

  dataSizeFormatter(row, cell, value, columnDef, dataContext) {
    const payloadSize = dataContext.payload_size || 0;
    const dataSize = dataContext.data_size || 0;

    // Show format: payload_size/data_size (no units)
    if (payloadSize === 0 && dataSize === 0) {
      return '-';
    } else if (payloadSize === dataSize) {
      // If they're the same, just show one value
      return dataSize.toString();
    } else {
      return `${payloadSize}/${dataSize}`;
    }
  },

  // Event handlers and utility methods
  updateGridDataFromEvent(data) {
    console.debug('Updating grid data from event:', data);
    try {
      if (data && data.grid_data) {
        // Update the dataset attribute
        this.el.dataset.gridData = JSON.stringify(data.grid_data);
        // Update the grid
        this.updateGridData();
      }
    } catch (error) {
      console.error('Error updating grid data from event:', error);
    }
  },

  handleExportEvent(data) {
    console.debug('Handling export event:', data);
    try {
      if (data && data.export_data) {
        // Create and trigger download
        const blob = new Blob([data.export_data], { type: data.content_type || 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = data.filename || 'export.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error handling export event:', error);
    }
  },

  // Export functionality is now handled server-side





  captureGroupStatesOnChange(groupingKey, isExpanded) {
    console.debug('Capturing group state change:', groupingKey, isExpanded);
    try {
      if (!this.groupStates) {
        this.groupStates = {};
      }
      this.groupStates[groupingKey] = isExpanded;
      console.debug('Updated group states:', this.groupStates);
    } catch (error) {
      console.error('Error capturing group state:', error);
    }
  },

  updateMessageCount() {
    try {
      if (this.dataView) {
        const countElement = document.getElementById('message-count-display');
        if (countElement) {
          if (this.filters.topic_grouping_enabled) {
            // Count unique topics when topic grouping is enabled
            const uniqueTopics = new Set();
            this.dataView.getItems().forEach(item => {
              if (item.topic && item.topic.trim() !== '') {
                uniqueTopics.add(item.topic);
              }
            });
            const topicCount = uniqueTopics.size;
            countElement.textContent = `${topicCount} topics`;
            console.debug('Updated topic count display to:', topicCount);
          } else {
            // Count messages when topic grouping is disabled
            const totalCount = this.dataView.getLength();
            countElement.textContent = `${totalCount} messages`;
            console.debug('Updated message count display to:', totalCount);
          }
        } else {
          console.warn('Message count display element not found');
        }
      }
    } catch (error) {
      console.error('Error updating message count:', error);
    }
  },

  handlePendingGroupStateRestoration() {
    console.debug('Handling pending group state restoration');
    try {
      if (this.groupStates && this.dataView && this.dataView.getGrouping) {
        const grouping = this.dataView.getGrouping();
        if (grouping && grouping.length > 0) {
          // Restore group states
          Object.keys(this.groupStates).forEach(groupKey => {
            const isExpanded = this.groupStates[groupKey];
            if (isExpanded) {
              this.dataView.expandGroup(groupKey);
            } else {
              this.dataView.collapseGroup(groupKey);
            }
          });
        }
      }
    } catch (error) {
      console.error('Error handling pending group state restoration:', error);
    }
  },

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  handleWindowResize() {
    console.debug('Handling window resize');
    try {
      if (this.grid) {
        // Update columns for new screen size
        this.columns = this.getResponsiveColumns();
        this.grid.setColumns(this.columns);

        // Update force fit setting
        this.grid.setOptions({
          forceFitColumns: this.shouldForceFitColumns()
        });

        // Resize canvas and fix positions
        this.grid.resizeCanvas();
        this.fixColumnPositions();
      }
    } catch (error) {
      console.error('Error handling window resize:', error);
    }
  },

  addNewMessageToGrid(messageData) {
    console.debug('Adding new message to grid:', messageData);
    try {
      if (!this.dataView) {
        console.warn('DataView not available for adding new message');
        return;
      }

      // Get current data
      const currentData = this.dataView.getItems();

      // Check if message with this ID already exists to avoid duplicates
      const existingMessage = currentData.find(item => item.id === messageData.id);
      if (existingMessage) {
        console.debug('Message with ID', messageData.id, 'already exists, skipping');
        return;
      }

      // Add the new message at the beginning (newest first)
      const newData = [messageData, ...currentData];

      // Update the data view
      this.dataView.setItems(newData);

      // Apply current grouping if enabled
      if (this.filters.topic_grouping_enabled) {
        this.updateGrouping(true);
      }

      // Force grid to render
      if (this.grid) {
        this.grid.invalidate();
        this.grid.render();
      }

      console.debug('New message added to grid successfully');
    } catch (error) {
      console.error('Error adding new message to grid:', error);
    }
  },

  clearGridData() {
    console.debug('Clearing grid data');
    try {
      if (this.dataView) {
        this.dataView.setItems([]);

        // Clear grouping
        this.dataView.setGrouping([]);

        // Force grid to render
        if (this.grid) {
          this.grid.invalidate();
          this.grid.render();
        }

        console.debug('Grid data cleared successfully');
      }
    } catch (error) {
      console.error('Error clearing grid data:', error);
    }
  }
};

export { TraceSlickGrid };
